#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC FreqTrade 立即亏损问题分析脚本

分析交易开仓后立即显示-0.10%亏损的根本原因：
1. 点差/滑点问题
2. 入场价格vs当前价格
3. 费用影响
4. 订单类型配置
5. 市场条件
6. 信号时机
"""

import sys
import logging
import json
import os
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ImmediateLossAnalyzer:
    """立即亏损问题分析器"""
    
    def __init__(self):
        self.freqtrade_config = self._load_freqtrade_config()
        self.smc_strategy_config = self._load_smc_strategy_config()
        
    def _load_freqtrade_config(self) -> Dict[str, Any]:
        """加载FreqTrade配置"""
        try:
            config_path = "freqtrade-bot/config.json"
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载FreqTrade配置失败: {e}")
            return {}
    
    def _load_smc_strategy_config(self) -> Dict[str, Any]:
        """加载SMC策略配置"""
        try:
            from backtest.strategies.smc_strategy import SMCStrategy
            strategy = SMCStrategy()
            return {
                'order_types': strategy.order_types,
                'stoploss': strategy.stoploss,
                'minimal_roi': strategy.minimal_roi,
                'timeframe': strategy.timeframe,
                'can_short': strategy.can_short
            }
        except Exception as e:
            logger.error(f"加载SMC策略配置失败: {e}")
            return {}

    def analyze_spread_slippage_issues(self):
        """分析点差/滑点问题"""
        logger.info("🔍 分析点差/滑点问题")
        
        issues = []
        recommendations = []
        
        # 1. 检查订单类型配置
        order_types = self.smc_strategy_config.get('order_types', {})
        entry_type = order_types.get('entry', 'unknown')
        exit_type = order_types.get('exit', 'unknown')
        
        logger.info(f"📋 当前订单类型配置:")
        logger.info(f"  入场订单: {entry_type}")
        logger.info(f"  出场订单: {exit_type}")
        
        if entry_type == 'market':
            issues.append("使用市价单入场可能导致滑点损失")
            recommendations.append("考虑使用限价单减少滑点")
            logger.warning("⚠️ 市价单入场 - 可能导致不利的成交价格")
        
        # 2. 检查价格配置
        entry_pricing = self.freqtrade_config.get('entry_pricing', {})
        price_side = entry_pricing.get('price_side', 'same')
        use_order_book = entry_pricing.get('use_order_book', False)
        
        logger.info(f"📋 入场价格配置:")
        logger.info(f"  价格侧: {price_side}")
        logger.info(f"  使用订单簿: {use_order_book}")
        
        if price_side == 'other':
            issues.append("price_side='other'可能导致不利的入场价格")
            logger.warning("⚠️ price_side='other' - 可能以对手价成交")
        
        # 3. 检查订单簿深度
        depth_check = entry_pricing.get('check_depth_of_market', {})
        if depth_check.get('enabled', False):
            delta = depth_check.get('bids_to_ask_delta', 0)
            logger.info(f"📋 市场深度检查: 启用, 买卖差价阈值: {delta}")
            if delta < 0.05:  # 5%
                issues.append("市场深度检查阈值过低，可能在流动性不足时成交")
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'severity': 'HIGH' if len(issues) > 2 else 'MEDIUM'
        }

    def analyze_fee_impact(self):
        """分析费用影响"""
        logger.info("💰 分析费用影响")
        
        issues = []
        recommendations = []
        
        # 1. 检查交易模式
        trading_mode = self.freqtrade_config.get('trading_mode', 'spot')
        logger.info(f"📋 交易模式: {trading_mode}")
        
        if trading_mode == 'futures':
            logger.info("📋 期货交易费用分析:")
            
            # 期货交易费用估算
            # Binance期货费用：Maker 0.02%, Taker 0.04%
            maker_fee = 0.0002  # 0.02%
            taker_fee = 0.0004  # 0.04%
            
            # 使用市价单通常是Taker费用
            order_types = self.smc_strategy_config.get('order_types', {})
            if order_types.get('entry') == 'market':
                entry_fee = taker_fee
                logger.info(f"  入场费用 (Taker): {entry_fee*100:.3f}%")
            else:
                entry_fee = maker_fee
                logger.info(f"  入场费用 (Maker): {entry_fee*100:.3f}%")
            
            if order_types.get('exit') == 'market':
                exit_fee = taker_fee
                logger.info(f"  出场费用 (Taker): {exit_fee*100:.3f}%")
            else:
                exit_fee = maker_fee
                logger.info(f"  出场费用 (Maker): {exit_fee*100:.3f}%")
            
            total_fee = entry_fee + exit_fee
            logger.info(f"  总费用: {total_fee*100:.3f}%")
            
            # 如果总费用接近0.10%，这可能是主要原因
            if total_fee >= 0.0008:  # 0.08%
                issues.append(f"交易费用过高: {total_fee*100:.3f}%")
                recommendations.append("考虑使用限价单降低费用")
            
            # 2. 资金费用（期货特有）
            logger.info("📋 资金费用分析:")
            logger.info("  资金费用每8小时收取一次")
            logger.info("  费率通常在-0.01%到+0.01%之间")
            logger.info("  持仓过夜可能产生额外费用")
            
            recommendations.append("避免在资金费用收取时间附近开仓")
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'estimated_fee_impact': total_fee * 100 if 'total_fee' in locals() else 0
        }

    def analyze_order_execution_timing(self):
        """分析订单执行时机"""
        logger.info("⏰ 分析订单执行时机")
        
        issues = []
        recommendations = []
        
        # 1. 检查时间框架
        timeframe = self.smc_strategy_config.get('timeframe', '1m')
        logger.info(f"📋 策略时间框架: {timeframe}")
        
        if timeframe == '1m':
            issues.append("1分钟时间框架可能导致信号噪音")
            recommendations.append("考虑使用5分钟或15分钟时间框架")
            logger.warning("⚠️ 1分钟高频交易容易受到市场噪音影响")
        
        # 2. 检查处理延迟配置
        internals = self.freqtrade_config.get('internals', {})
        process_throttle = internals.get('process_throttle_secs', 5)
        logger.info(f"📋 处理延迟: {process_throttle}秒")
        
        if process_throttle > 2:
            issues.append("处理延迟过长可能导致信号过时")
            recommendations.append("减少process_throttle_secs到1-2秒")
        
        # 3. 检查新K线处理
        process_new_candles = self.freqtrade_config.get('process_only_new_candles', True)
        logger.info(f"📋 仅处理新K线: {process_new_candles}")
        
        if not process_new_candles:
            issues.append("处理所有K线可能导致重复信号")
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'severity': 'MEDIUM'
        }

    def analyze_market_conditions(self):
        """分析市场条件影响"""
        logger.info("📈 分析市场条件影响")
        
        issues = []
        recommendations = []
        
        # 1. 检查交易对配置
        pair_whitelist = self.freqtrade_config.get('exchange', {}).get('pair_whitelist', [])
        logger.info(f"📋 交易对数量: {len(pair_whitelist)}")
        
        # 分析交易对特征
        major_pairs = [p for p in pair_whitelist if any(major in p for major in ['BTC', 'ETH', 'BNB'])]
        minor_pairs = [p for p in pair_whitelist if p not in major_pairs]
        
        logger.info(f"  主要交易对: {len(major_pairs)}")
        logger.info(f"  次要交易对: {len(minor_pairs)}")
        
        if len(minor_pairs) > len(major_pairs):
            issues.append("次要交易对比例过高，流动性可能不足")
            recommendations.append("增加主要交易对比例")
        
        # 2. 检查最大开仓数
        max_open_trades = self.freqtrade_config.get('max_open_trades', 1)
        logger.info(f"📋 最大开仓数: {max_open_trades}")
        
        if max_open_trades > 10:
            issues.append("同时开仓过多可能分散资金，影响单笔收益")
            recommendations.append("考虑减少最大开仓数到5-8个")
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'pair_analysis': {
                'total': len(pair_whitelist),
                'major': len(major_pairs),
                'minor': len(minor_pairs)
            }
        }

    def analyze_strategy_parameters(self):
        """分析策略参数"""
        logger.info("⚙️ 分析策略参数")
        
        issues = []
        recommendations = []
        
        # 1. 检查止损设置
        stoploss = self.smc_strategy_config.get('stoploss', 0)
        logger.info(f"📋 止损设置: {stoploss*100:.1f}%")
        
        if abs(stoploss) < 0.015:  # 小于1.5%
            issues.append("止损设置过紧，可能被市场噪音触发")
            recommendations.append("考虑将止损设置为-2%到-3%")
        
        # 2. 检查ROI设置
        minimal_roi = self.smc_strategy_config.get('minimal_roi', {})
        logger.info(f"📋 最小ROI配置: {minimal_roi}")
        
        first_roi = list(minimal_roi.values())[0] if minimal_roi else 0
        if first_roi < 0.02:  # 小于2%
            issues.append("初始ROI目标过低，可能过早出场")
            recommendations.append("考虑将初始ROI设置为2-3%")
        
        return {
            'issues': issues,
            'recommendations': recommendations,
            'current_stoploss': stoploss * 100,
            'current_roi': first_roi * 100 if first_roi else 0
        }

    def generate_optimization_recommendations(self):
        """生成优化建议"""
        logger.info("🎯 生成优化建议")
        
        # 收集所有分析结果
        spread_analysis = self.analyze_spread_slippage_issues()
        fee_analysis = self.analyze_fee_impact()
        timing_analysis = self.analyze_order_execution_timing()
        market_analysis = self.analyze_market_conditions()
        strategy_analysis = self.analyze_strategy_parameters()
        
        # 综合建议
        all_issues = []
        all_recommendations = []
        
        for analysis in [spread_analysis, fee_analysis, timing_analysis, market_analysis, strategy_analysis]:
            all_issues.extend(analysis.get('issues', []))
            all_recommendations.extend(analysis.get('recommendations', []))
        
        # 优先级排序
        high_priority = []
        medium_priority = []
        low_priority = []
        
        # 根据问题类型分配优先级
        for issue in all_issues:
            if any(keyword in issue.lower() for keyword in ['费用', '市价单', '滑点']):
                high_priority.append(issue)
            elif any(keyword in issue.lower() for keyword in ['时间框架', '延迟']):
                medium_priority.append(issue)
            else:
                low_priority.append(issue)
        
        return {
            'summary': {
                'total_issues': len(all_issues),
                'high_priority': len(high_priority),
                'medium_priority': len(medium_priority),
                'low_priority': len(low_priority)
            },
            'issues': {
                'high': high_priority,
                'medium': medium_priority,
                'low': low_priority
            },
            'recommendations': all_recommendations,
            'estimated_fee_impact': fee_analysis.get('estimated_fee_impact', 0)
        }

    def create_optimized_config(self):
        """创建优化后的配置"""
        logger.info("🔧 创建优化配置")
        
        # 优化后的FreqTrade配置
        optimized_freqtrade_config = self.freqtrade_config.copy()
        
        # 1. 优化入场价格配置
        optimized_freqtrade_config['entry_pricing'] = {
            "price_side": "same",  # 使用同侧价格
            "use_order_book": True,
            "order_book_top": 1,
            "price_last_balance": 0.0,
            "check_depth_of_market": {
                "enabled": True,
                "bids_to_ask_delta": 0.05  # 提高到5%
            }
        }
        
        # 2. 优化处理延迟
        optimized_freqtrade_config['internals']['process_throttle_secs'] = 1
        
        # 3. 减少最大开仓数
        optimized_freqtrade_config['max_open_trades'] = 8
        
        # 优化后的策略配置建议
        optimized_strategy_config = {
            'order_types': {
                'entry': 'limit',  # 改为限价单
                'exit': 'limit',   # 改为限价单
                'stoploss': 'market',
                'stoploss_on_exchange': True,  # 启用交易所止损
                'stoploss_on_exchange_interval': 60,
            },
            'stoploss': -0.025,  # 调整止损到-2.5%
            'timeframe': '5m',   # 改为5分钟时间框架
        }
        
        return {
            'freqtrade_config': optimized_freqtrade_config,
            'strategy_config': optimized_strategy_config
        }

def main():
    """主函数"""
    logger.info("🚀 开始SMC FreqTrade立即亏损问题分析")
    
    analyzer = ImmediateLossAnalyzer()
    
    # 执行分析
    logger.info("="*80)
    logger.info("📊 立即亏损问题根因分析报告")
    logger.info("="*80)
    
    # 生成优化建议
    recommendations = analyzer.generate_optimization_recommendations()
    
    # 显示分析结果
    logger.info(f"\n📋 问题总结:")
    logger.info(f"  发现问题总数: {recommendations['summary']['total_issues']}")
    logger.info(f"  高优先级: {recommendations['summary']['high_priority']}")
    logger.info(f"  中优先级: {recommendations['summary']['medium_priority']}")
    logger.info(f"  低优先级: {recommendations['summary']['low_priority']}")
    
    if recommendations['estimated_fee_impact'] > 0:
        logger.info(f"  估计费用影响: {recommendations['estimated_fee_impact']:.3f}%")
    
    logger.info(f"\n🚨 高优先级问题:")
    for issue in recommendations['issues']['high']:
        logger.info(f"  • {issue}")
    
    logger.info(f"\n💡 主要优化建议:")
    for rec in recommendations['recommendations'][:5]:  # 显示前5个建议
        logger.info(f"  • {rec}")
    
    # 创建优化配置
    optimized_configs = analyzer.create_optimized_config()
    
    logger.info(f"\n🎯 关键优化点:")
    logger.info(f"  • 改用限价单减少滑点")
    logger.info(f"  • 调整价格配置避免不利成交")
    logger.info(f"  • 减少处理延迟提高响应速度")
    logger.info(f"  • 优化止损和ROI设置")
    
    logger.info("="*80)
    logger.info("✅ 分析完成")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
