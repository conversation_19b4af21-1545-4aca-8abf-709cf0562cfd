#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
策略基类模块

提供交易策略的基础类，所有具体的策略都应该继承此类。
"""

import abc
from typing import Dict, Any, Union, Optional, List, Tuple
import pandas as pd
import numpy as np

from ..base import Strategy


class BaseStrategy(Strategy):
    """
    策略基类
    
    提供所有策略共用的基础功能，具体策略通过继承此类实现。
    """
    
    def __init__(self, **params):
        """
        初始化策略
        
        Parameters
        ----------
        **params : dict
            策略参数
        """
        # 设置默认参数
        default_params = {
            'name': self.__class__.__name__,
            'description': '策略描述未设置',
        }
        
        # 合并默认参数和用户参数
        merged_params = default_params.copy()
        merged_params.update(params)
        
        super().__init__(**merged_params)
        
        # 初始化其他属性
        self.indicators = {}  # 用于存储计算的指标
        self.data = None  # 输入数据的引用
        
    def prepare_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        预处理数据，计算策略所需的指标
        
        Parameters
        ----------
        data : pd.DataFrame
            输入的市场数据
            
        Returns
        -------
        pd.DataFrame
            处理后的数据，可能包含新的指标列
        """
        # 存储数据引用
        self.data = data
        
        # 子类应该重写此方法以添加自己的指标计算
        return data
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        生成交易信号
        
        Parameters
        ----------
        data : pd.DataFrame
            市场数据
            
        Returns
        -------
        pd.DataFrame
            包含交易信号的DataFrame
        """
        # 预处理数据
        processed_data = self.prepare_data(data)
        
        # 子类必须实现信号生成逻辑
        signals = self._generate_signals_impl(processed_data)
        
        return signals
    
    @abc.abstractmethod
    def _generate_signals_impl(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        具体信号生成逻辑的实现
        
        Parameters
        ----------
        data : pd.DataFrame
            预处理后的市场数据
            
        Returns
        -------
        pd.DataFrame
            包含交易信号的DataFrame
        """
        pass
    
    def add_indicator(self, name: str, indicator: pd.Series) -> None:
        """
        添加计算的指标到策略
        
        Parameters
        ----------
        name : str
            指标名称
        indicator : pd.Series
            指标数据
        """
        self.indicators[name] = indicator
    
    def get_indicator(self, name: str) -> Optional[pd.Series]:
        """
        获取指定名称的指标
        
        Parameters
        ----------
        name : str
            指标名称
            
        Returns
        -------
        pd.Series or None
            指标数据，如果不存在则返回None
        """
        return self.indicators.get(name)
    
    def plot_indicators(self, ax=None, **kwargs) -> None:
        """
        绘制策略的指标
        
        Parameters
        ----------
        ax : matplotlib.axes.Axes, optional
            绘图轴，默认为None
        **kwargs : dict
            绘图参数
        """
        import matplotlib.pyplot as plt
        
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))
        
        # 绘制所有指标
        for name, indicator in self.indicators.items():
            indicator.plot(ax=ax, label=name)
        
        # 设置图表属性
        ax.set_title(f"{self.params['name']} Indicators")
        ax.grid(True)
        ax.legend()
        
        if ax is None:
            plt.tight_layout()
            plt.show()
    
    def __str__(self) -> str:
        """
        返回策略的字符串表示
        
        Returns
        -------
        str
            策略描述
        """
        return f"{self.params['name']}: {self.params['description']}" 