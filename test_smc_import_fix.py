#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略导入修复验证脚本
验证FreqTrade环境中的导入依赖问题是否已解决

功能：
1. 测试策略导入是否成功
2. 验证指标计算功能
3. 检查警告日志输出
4. 确认功能等价性
"""

import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_strategy_import():
    """测试策略导入"""
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        logger.info("✅ SMC策略导入成功")
        return SMCStrategy
    except Exception as e:
        logger.error(f"❌ SMC策略导入失败: {e}")
        return None

def test_import_manager():
    """测试导入管理器"""
    try:
        from backtest.strategies.smc_strategy import _import_manager
        
        logger.info(f"FreqTrade可用: {_import_manager.freqtrade_available}")
        logger.info(f"高级指标可用: {_import_manager.advanced_indicators_available}")
        logger.info(f"特征模块可用: {_import_manager.features_module_available}")
        
        # 测试特征计算函数
        if _import_manager.calculate_moving_averages and _import_manager.calculate_rsi:
            logger.info("✅ 特征计算函数可用")
            return True
        else:
            logger.warning("⚠️ 使用降级特征计算函数")
            return True
            
    except Exception as e:
        logger.error(f"❌ 导入管理器测试失败: {e}")
        return False

def create_test_data(length=1000):
    """创建测试数据"""
    np.random.seed(42)
    
    # 生成模拟OHLCV数据
    dates = pd.date_range('2024-01-01', periods=length, freq='1min')
    
    # 生成价格数据（随机游走）
    returns = np.random.normal(0, 0.001, length)
    price = 50000 * np.exp(np.cumsum(returns))
    
    # 生成OHLCV
    data = pd.DataFrame({
        'timestamp': dates,
        'open': price * (1 + np.random.normal(0, 0.0001, length)),
        'high': price * (1 + np.abs(np.random.normal(0, 0.002, length))),
        'low': price * (1 - np.abs(np.random.normal(0, 0.002, length))),
        'close': price,
        'volume': np.random.uniform(100, 1000, length)
    })
    
    # 确保high >= low
    data['high'] = np.maximum(data['high'], data['low'])
    data['high'] = np.maximum(data['high'], data['open'])
    data['high'] = np.maximum(data['high'], data['close'])
    data['low'] = np.minimum(data['low'], data['open'])
    data['low'] = np.minimum(data['low'], data['close'])
    
    data.set_index('timestamp', inplace=True)
    return data

def test_indicator_calculation(strategy_class):
    """测试指标计算"""
    try:
        # 创建策略实例
        strategy = strategy_class()
        
        # 创建测试数据
        test_data = create_test_data(500)
        logger.info(f"测试数据创建完成: {len(test_data)} 行")
        
        # 测试指标计算
        result_data = strategy._prepare_smc_indicators(test_data.copy())
        
        # 检查必要的指标是否存在
        required_indicators = ['EMA_20', 'EMA_50', 'EMA_200', 'RSI', 'ATR']
        missing_indicators = []
        
        for indicator in required_indicators:
            if indicator not in result_data.columns:
                missing_indicators.append(indicator)
        
        if missing_indicators:
            logger.error(f"❌ 缺少指标: {missing_indicators}")
            return False
        else:
            logger.info("✅ 所有必要指标计算成功")
            
        # 检查指标数据质量
        for indicator in required_indicators:
            valid_count = result_data[indicator].notna().sum()
            total_count = len(result_data)
            valid_ratio = valid_count / total_count
            
            logger.info(f"{indicator}: 有效数据比例 {valid_ratio:.2%} ({valid_count}/{total_count})")
            
            if valid_ratio < 0.5:  # 至少50%的数据应该是有效的
                logger.warning(f"⚠️ {indicator} 有效数据比例过低")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 指标计算测试失败: {e}")
        return False

def test_freqtrade_methods(strategy_class):
    """测试FreqTrade方法"""
    try:
        strategy = strategy_class()
        test_data = create_test_data(200)
        metadata = {'pair': 'BTC/USDT'}
        
        # 测试populate_indicators
        indicators_data = strategy.populate_indicators(test_data.copy(), metadata)
        logger.info("✅ populate_indicators 方法测试成功")
        
        # 测试populate_entry_trend
        entry_data = strategy.populate_entry_trend(indicators_data.copy(), metadata)
        logger.info("✅ populate_entry_trend 方法测试成功")
        
        # 测试populate_exit_trend
        exit_data = strategy.populate_exit_trend(entry_data.copy(), metadata)
        logger.info("✅ populate_exit_trend 方法测试成功")
        
        # 检查信号生成
        entry_signals = 0
        exit_signals = 0
        
        if 'enter_long' in exit_data.columns:
            entry_signals += exit_data['enter_long'].sum()
        if 'enter_short' in exit_data.columns:
            entry_signals += exit_data['enter_short'].sum()
        if 'exit_long' in exit_data.columns:
            exit_signals += exit_data['exit_long'].sum()
        if 'exit_short' in exit_data.columns:
            exit_signals += exit_data['exit_short'].sum()
            
        logger.info(f"信号统计 - 入场信号: {entry_signals}, 出场信号: {exit_signals}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ FreqTrade方法测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始SMC策略导入修复验证")
    
    # 测试1: 策略导入
    strategy_class = test_strategy_import()
    if not strategy_class:
        return False
    
    # 测试2: 导入管理器
    if not test_import_manager():
        return False
    
    # 测试3: 指标计算
    if not test_indicator_calculation(strategy_class):
        return False
    
    # 测试4: FreqTrade方法
    if not test_freqtrade_methods(strategy_class):
        return False
    
    logger.info("🎉 所有测试通过！SMC策略导入修复验证成功")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
