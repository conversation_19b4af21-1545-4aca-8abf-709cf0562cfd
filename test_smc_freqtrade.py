#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FreqTrade SMC策略测试脚本
验证策略能正确生成信号并计算基本指标
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.storage.optimized_storage import OptimizedStorage
from backtest.strategies.smc_strategy import SMCStrategy
from config.smc_strategy_config import SMCConfigManager

def test_smc_strategy():
    print("🧪 FreqTrade SMC策略测试")
    print("=" * 50)
    
    # 加载数据
    storage = OptimizedStorage('./data/storage/data')
    data = storage.load_data('BTC_USDT', '1m')
    print(f"数据长度: {len(data)}")
    print(f"数据周期: {data.index[0]} 到 {data.index[-1]}")
    
    # 创建策略
    config_manager = SMCConfigManager()
    strategy_params = config_manager.get_strategy_params()
    
    # FreqTrade配置
    config = {'timeframe': '1m'}
    strategy_params['config'] = config
    strategy = SMCStrategy(**strategy_params)
    
    print(f"\n策略参数:")
    for key, value in strategy_params.items():
        if key != 'config':
            print(f"  {key}: {value}")
    
    # 测试策略方法
    print(f"\n🔧 测试策略方法...")
    
    # 1. 测试指标计算
    print("1. 计算指标...")
    dataframe = strategy.populate_indicators(data.copy(), {})
    print(f"   指标列: {[col for col in dataframe.columns if col not in data.columns]}")
    
    # 2. 测试入场信号
    print("2. 生成入场信号...")
    dataframe = strategy.populate_entry_trend(dataframe, {})
    
    long_signals = dataframe['enter_long'].sum() if 'enter_long' in dataframe.columns else 0
    short_signals = dataframe['enter_short'].sum() if 'enter_short' in dataframe.columns else 0
    
    print(f"   多头信号: {long_signals}")
    print(f"   空头信号: {short_signals}")
    print(f"   总信号: {long_signals + short_signals}")
    
    # 3. 测试出场信号
    print("3. 生成出场信号...")
    dataframe = strategy.populate_exit_trend(dataframe, {})
    
    exit_long = dataframe['exit_long'].sum() if 'exit_long' in dataframe.columns else 0
    exit_short = dataframe['exit_short'].sum() if 'exit_short' in dataframe.columns else 0
    
    print(f"   多头出场: {exit_long}")
    print(f"   空头出场: {exit_short}")
    
    # 4. 分析信号质量
    print(f"\n📊 信号质量分析:")
    
    total_signals = long_signals + short_signals
    signal_density = total_signals / len(data) * 100
    
    print(f"   信号密度: {signal_density:.1f}%")
    print(f"   平均每天信号: {total_signals * 1440 / len(data):.1f}个")
    
    # 信号频率分类
    if signal_density > 30:
        frequency_type = "超高频"
    elif signal_density > 15:
        frequency_type = "高频"
    elif signal_density > 5:
        frequency_type = "中频"
    else:
        frequency_type = "低频"
    
    print(f"   策略类型: {frequency_type}策略")
    
    # 5. 检查关键指标
    print(f"\n📈 关键指标检查:")
    
    # EMA趋势
    if 'EMA_20' in dataframe.columns and 'EMA_50' in dataframe.columns:
        bullish_trend = (dataframe['EMA_20'] > dataframe['EMA_50']).sum()
        bearish_trend = (dataframe['EMA_20'] < dataframe['EMA_50']).sum()
        trend_ratio = bullish_trend / len(dataframe) * 100
        
        print(f"   多头趋势时间: {trend_ratio:.1f}%")
        print(f"   空头趋势时间: {100-trend_ratio:.1f}%")
    
    # RSI分布
    if 'RSI' in dataframe.columns:
        rsi_mean = dataframe['RSI'].mean()
        rsi_std = dataframe['RSI'].std()
        
        print(f"   RSI均值: {rsi_mean:.1f}")
        print(f"   RSI标准差: {rsi_std:.1f}")
        
        # RSI区间分布
        oversold = (dataframe['RSI'] < 30).sum()
        overbought = (dataframe['RSI'] > 70).sum()
        neutral = len(dataframe) - oversold - overbought
        
        print(f"   RSI分布: 超卖{oversold/len(dataframe)*100:.1f}% | 中性{neutral/len(dataframe)*100:.1f}% | 超买{overbought/len(dataframe)*100:.1f}%")
    
    # 6. 显示信号示例
    if total_signals > 0:
        print(f"\n🎯 信号示例 (前5个):")
        
        if long_signals > 0:
            long_indices = dataframe[dataframe['enter_long'] == 1].index[:5]
            print(f"   多头信号时间: {[str(idx) for idx in long_indices]}")
        
        if short_signals > 0:
            short_indices = dataframe[dataframe['enter_short'] == 1].index[:5]
            print(f"   空头信号时间: {[str(idx) for idx in short_indices]}")
    
    # 7. 策略配置摘要
    print(f"\n⚙️ 策略配置摘要:")
    print(f"   时间框架: {strategy.timeframe}")
    print(f"   止损: {strategy.stoploss*100:.1f}%")
    print(f"   最小ROI: {strategy.minimal_roi}")
    print(f"   启动蜡烛数: {strategy.startup_candle_count}")
    print(f"   支持做空: {strategy.can_short}")
    
    print(f"\n✅ 策略测试完成！")
    
    # 返回结果摘要
    return {
        'total_signals': total_signals,
        'long_signals': long_signals,
        'short_signals': short_signals,
        'signal_density': signal_density,
        'frequency_type': frequency_type,
        'data_points': len(data)
    }

if __name__ == "__main__":
    results = test_smc_strategy()
    print(f"\n📋 测试结果摘要:")
    for key, value in results.items():
        print(f"   {key}: {value}") 