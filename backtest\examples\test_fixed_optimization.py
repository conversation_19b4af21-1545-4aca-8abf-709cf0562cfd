#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复后的SMC策略优化器

验证优化器能够正常工作并产生有意义的结果
"""

import pandas as pd
import numpy as np
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from data.storage.optimized_storage import OptimizedStorage
from backtest.strategies.optimization.smc_optimizer import SMCOptimizer, FreqTradeOptimizationEngine
from config.smc_strategy_config import SMCConfigManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_small_optimization_test():
    """运行小规模优化测试"""
    print("=== 运行小规模优化测试 ===")
    
    # 加载数据
    storage_dir = "./data/storage/data"
    storage = OptimizedStorage(storage_dir)
    
    symbol = 'BTC_USDT'
    timeframe = '1m'
    
    if not storage.has_data(symbol, timeframe):
        print(f"错误: {symbol} 的数据不存在")
        return False
    
    data = storage.load_data(symbol, timeframe)
    # 使用较小的数据集进行快速测试
    data = data.tail(5000)
    print(f"测试数据长度: {len(data)}")
    
    # 创建配置管理器
    config_manager = SMCConfigManager()
    
    # 临时修改优化参数以进行快速测试
    original_max_combinations = config_manager.optimization_params.max_combinations
    config_manager.optimization_params.max_combinations = 50  # 只测试50个组合
    
    print(f"最大组合数限制: {config_manager.optimization_params.max_combinations}")
    print(f"最小交易数要求: {config_manager.optimization_params.min_trades_required}")
    
    # 创建优化器
    optimizer = SMCOptimizer(
        data=data,
        config_manager=config_manager,
        engine_class=FreqTradeOptimizationEngine
    )
    
    # 运行优化
    print("\n开始运行优化...")
    try:
        results = optimizer.run_grid_search(
            max_workers=1,
            save_results=True,
            results_dir="./backtest/examples/output/test_optimization"
        )
        
        print(f"\n优化完成!")
        print(f"最佳得分: {results.best_score:.4f}")
        print(f"完成组合数: {results.completed_combinations}")
        print(f"有效结果数: {len(results.all_results)}")
        print(f"优化时间: {results.optimization_time:.2f}秒")
        print(f"目标指标: {results.target_metric}")
        
        if results.best_params:
            print(f"\n最佳参数:")
            for key, value in results.best_params.items():
                if key != 'config':  # 跳过config参数
                    print(f"  {key}: {value}")
        
        # 恢复原始设置
        config_manager.optimization_params.max_combinations = original_max_combinations
        
        return results.best_score > 0.0
        
    except Exception as e:
        print(f"优化失败: {e}")
        import traceback
        traceback.print_exc()
        
        # 恢复原始设置
        config_manager.optimization_params.max_combinations = original_max_combinations
        return False

def analyze_optimization_grid():
    """分析优化网格配置"""
    print("\n=== 分析优化网格配置 ===")
    
    config_manager = SMCConfigManager()
    opt_grid = config_manager.get_optimization_grid()
    
    print("当前优化网格:")
    total_combinations = 1
    for param_name, param_values in opt_grid.items():
        total_combinations *= len(param_values)
        print(f"  {param_name}: {param_values} ({len(param_values)} 个值)")
    
    print(f"\n总参数组合数: {total_combinations:,}")
    
    # 计算在不同限制下的实际组合数
    max_combinations = config_manager.optimization_params.max_combinations
    actual_combinations = min(total_combinations, max_combinations)
    
    print(f"最大组合数限制: {max_combinations:,}")
    print(f"实际测试组合数: {actual_combinations:,}")
    
    if total_combinations > max_combinations:
        coverage = (max_combinations / total_combinations) * 100
        print(f"参数空间覆盖率: {coverage:.2f}%")
    else:
        print("参数空间覆盖率: 100%")
    
    return actual_combinations

def test_parameter_evaluation_speed():
    """测试参数评估速度"""
    print("\n=== 测试参数评估速度 ===")
    
    # 加载数据
    storage_dir = "./data/storage/data"
    storage = OptimizedStorage(storage_dir)
    
    symbol = 'BTC_USDT'
    timeframe = '1m'
    data = storage.load_data(symbol, timeframe)
    data = data.tail(2000)  # 使用2000行数据
    
    # 创建优化器
    config_manager = SMCConfigManager()
    optimizer = SMCOptimizer(
        data=data,
        config_manager=config_manager,
        engine_class=FreqTradeOptimizationEngine
    )
    
    # 生成参数组合
    param_combinations = optimizer._generate_param_combinations()
    test_combinations = param_combinations[:10]  # 测试前10个组合
    
    print(f"测试 {len(test_combinations)} 个参数组合的评估速度...")
    
    import time
    start_time = time.time()
    
    valid_results = 0
    for i, params in enumerate(test_combinations):
        try:
            result = optimizer._evaluate_params(params)
            if result is not None:
                valid_results += 1
        except Exception as e:
            print(f"组合 {i+1} 评估失败: {e}")
    
    end_time = time.time()
    total_time = end_time - start_time
    avg_time = total_time / len(test_combinations)
    
    print(f"总评估时间: {total_time:.2f}秒")
    print(f"平均每个组合: {avg_time:.3f}秒")
    print(f"有效结果: {valid_results}/{len(test_combinations)}")
    print(f"成功率: {(valid_results/len(test_combinations)*100):.1f}%")
    
    # 估算完整优化时间
    max_combinations = config_manager.optimization_params.max_combinations
    estimated_time = avg_time * max_combinations
    
    print(f"\n估算完整优化时间 ({max_combinations} 组合): {estimated_time:.1f}秒 ({estimated_time/60:.1f}分钟)")
    
    return valid_results > 0

def main():
    """主测试函数"""
    print("SMC策略优化器修复验证测试")
    print("=" * 60)
    
    # 测试1: 分析优化网格
    actual_combinations = analyze_optimization_grid()
    
    # 测试2: 参数评估速度
    speed_test_success = test_parameter_evaluation_speed()
    
    # 测试3: 小规模优化测试
    optimization_success = run_small_optimization_test()
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"优化网格配置: 正常 ({actual_combinations:,} 个组合)")
    print(f"参数评估速度: {'正常' if speed_test_success else '异常'}")
    print(f"优化功能测试: {'成功' if optimization_success else '失败'}")
    
    if optimization_success:
        print("\n✅ 优化器修复成功！")
        print("主要修复内容:")
        print("1. 修复了交易记录为空的问题")
        print("2. 降低了最小交易数要求 (50 -> 10)")
        print("3. 优化了参数网格，减少了组合数量")
        print("4. 提高了优化效率和成功率")
    else:
        print("\n❌ 优化器仍存在问题，需要进一步调试")
    
    return optimization_success

if __name__ == "__main__":
    main()
