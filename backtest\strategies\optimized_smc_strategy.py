#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化版SMC策略 - 解决立即亏损问题
基于原SMC策略，针对立即亏损问题进行优化

主要优化：
1. 改用限价单减少滑点
2. 调整时间框架减少噪音  
3. 智能入场价格确认
4. 优化止损和ROI设置
"""

from backtest.strategies.smc_strategy import SMCStrategy
from typing import Optional
import logging

logger = logging.getLogger(__name__)


# SMC策略优化配置 - 解决立即亏损问题

class OptimizedSMCStrategy(SMCStrategy):
    '''优化版SMC策略 - 减少立即亏损'''
    
    # 🔧 关键优化1: 改用限价单减少滑点
    order_types = {
        'entry': 'limit',      # 改为限价单
        'exit': 'limit',       # 改为限价单  
        'stoploss': 'market',  # 止损保持市价单
        'stoploss_on_exchange': True,  # 启用交易所止损
        'stoploss_on_exchange_interval': 60,
    }
    
    # 🔧 关键优化2: 调整时间框架减少噪音
    timeframe = '5m'  # 从1分钟改为5分钟
    
    # 🔧 关键优化3: 调整止损避免过早触发
    stoploss = -0.025  # 从-2%调整到-2.5%
    
    # 🔧 关键优化4: 优化ROI设置
    minimal_roi = {
        "0": 0.025,   # 初始目标2.5%
        "30": 0.02,   # 30分钟后2%
        "60": 0.015,  # 1小时后1.5%
        "120": 0.01   # 2小时后1%
    }
    
    # 🔧 关键优化5: 增加入场确认
    def confirm_trade_entry(self, pair: str, order_type: str, amount: float, 
                           rate: float, time_in_force: str, current_time, 
                           entry_tag: Optional[str], side: str, **kwargs) -> bool:
        '''增强的入场确认 - 避免不利价格成交'''
        
        # 获取当前市场价格
        try:
            ticker = self.dp.ticker(pair)
            if ticker:
                bid = ticker.get('bid', 0)
                ask = ticker.get('ask', 0)
                
                if side == 'long':
                    # 做多：确保入场价格不高于ask价格的0.05%
                    if rate > ask * 1.0005:
                        logger.warning(f"⚠️ 做多入场价格过高 - {pair}: {rate} > {ask * 1.0005}")
                        return False
                        
                elif side == 'short':
                    # 做空：确保入场价格不低于bid价格的0.05%
                    if rate < bid * 0.9995:
                        logger.warning(f"⚠️ 做空入场价格过低 - {pair}: {rate} < {bid * 0.9995}")
                        return False
                
                logger.info(f"✅ 入场价格确认通过 - {pair} {side}: {rate}")
                
        except Exception as e:
            logger.debug(f"入场确认检查失败: {e}")
        
        return True
    
    # 🔧 关键优化6: 智能限价单价格计算
    def custom_entry_price(self, pair: str, current_time, proposed_rate: float,
                          entry_tag: Optional[str], side: str, **kwargs) -> float:
        '''智能限价单价格计算'''
        
        try:
            ticker = self.dp.ticker(pair)
            if not ticker:
                return proposed_rate
            
            bid = ticker.get('bid', 0)
            ask = ticker.get('ask', 0)
            spread = (ask - bid) / bid if bid > 0 else 0
            
            if side == 'long':
                # 做多：使用略高于bid的价格，但不超过mid价格
                mid_price = (bid + ask) / 2
                optimal_price = min(bid * 1.0002, mid_price)  # bid + 0.02%
                logger.info(f"📊 做多限价优化 - {pair}: {proposed_rate:.6f} -> {optimal_price:.6f}")
                return optimal_price
                
            elif side == 'short':
                # 做空：使用略低于ask的价格，但不低于mid价格  
                mid_price = (bid + ask) / 2
                optimal_price = max(ask * 0.9998, mid_price)  # ask - 0.02%
                logger.info(f"📊 做空限价优化 - {pair}: {proposed_rate:.6f} -> {optimal_price:.6f}")
                return optimal_price
                
        except Exception as e:
            logger.debug(f"限价单价格优化失败: {e}")
        
        return proposed_rate

