#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略时区和做空盈亏修复验证脚本
验证关键问题的修复效果

关键修复：
1. confirm_trade_exit方法中的时区错误
2. 做空盈亏计算逻辑验证
3. 所有datetime操作的时区安全性
"""

import sys
import logging
from datetime import datetime, timezone, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockTrade:
    """模拟FreqTrade交易对象"""
    def __init__(self, is_short=True, open_rate=0.189500, open_date=None):
        self.is_short = is_short
        self.open_rate = open_rate
        self.open_date = open_date or datetime.now(timezone.utc) - timedelta(hours=1)
    
    def calc_profit_ratio(self, exit_rate):
        """模拟FreqTrade的盈亏比例计算"""
        if self.is_short:
            # 做空：入场价 > 出场价 = 盈利
            return (self.open_rate - exit_rate) / self.open_rate
        else:
            # 做多：出场价 > 入场价 = 盈利
            return (exit_rate - self.open_rate) / self.open_rate
    
    def calc_profit(self, exit_rate, stake_amount=1000):
        """模拟FreqTrade的绝对盈亏计算"""
        profit_ratio = self.calc_profit_ratio(exit_rate)
        return profit_ratio * stake_amount

def test_timezone_fix():
    """测试时区修复"""
    logger.info("🔧 测试时区修复")
    
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        
        strategy = SMCStrategy()
        
        # 测试场景：时区不匹配的情况
        current_time_naive = datetime(2025, 6, 12, 18, 0, 0)  # 无时区
        open_date_utc = datetime(2025, 6, 12, 17, 0, 0, tzinfo=timezone.utc)  # UTC时区
        
        trade = MockTrade(
            is_short=True,
            open_rate=0.189500,
            open_date=open_date_utc
        )
        
        logger.info("测试confirm_trade_exit方法...")
        
        # 这个调用之前会崩溃，现在应该正常工作
        result = strategy.confirm_trade_exit(
            pair="ADA/USDT:USDT",
            trade=trade,
            order_type="market",
            amount=963,
            rate=0.189780,  # 价格上涨
            time_in_force="GTC",
            exit_reason="exit_signal",
            current_time=current_time_naive
        )
        
        logger.info(f"✅ confirm_trade_exit执行成功，返回: {result}")
        return True
        
    except Exception as e:
        logger.error(f"❌ 时区修复测试失败: {e}")
        return False

def test_short_pnl_logic():
    """测试做空盈亏逻辑"""
    logger.info("📊 测试做空盈亏逻辑")
    
    try:
        # 测试场景1：价格上涨，做空应该亏损
        logger.info("场景1: 价格上涨，做空亏损")
        trade1 = MockTrade(is_short=True, open_rate=0.189500)
        exit_rate1 = 0.189780  # 价格上涨
        
        profit_ratio1 = trade1.calc_profit_ratio(exit_rate1)
        profit_abs1 = trade1.calc_profit(exit_rate1)
        
        price_change1 = ((exit_rate1 - trade1.open_rate) / trade1.open_rate) * 100
        
        logger.info(f"  入场价: {trade1.open_rate:.6f}")
        logger.info(f"  出场价: {exit_rate1:.6f}")
        logger.info(f"  价格变化: {price_change1:+.2f}% (上涨)")
        logger.info(f"  做空盈亏: {(profit_ratio1 * 100):+.2f}%")
        logger.info(f"  绝对盈亏: {profit_abs1:+.2f} USDT")
        
        # 验证逻辑：价格上涨，做空应该亏损
        if profit_ratio1 < 0:
            logger.info("  ✅ 逻辑正确：价格上涨，做空亏损")
            scenario1_correct = True
        else:
            logger.error("  ❌ 逻辑错误：价格上涨，做空应该亏损")
            scenario1_correct = False
        
        # 测试场景2：价格下跌，做空应该盈利
        logger.info("\n场景2: 价格下跌，做空盈利")
        trade2 = MockTrade(is_short=True, open_rate=0.189500)
        exit_rate2 = 0.188000  # 价格下跌
        
        profit_ratio2 = trade2.calc_profit_ratio(exit_rate2)
        profit_abs2 = trade2.calc_profit(exit_rate2)
        
        price_change2 = ((exit_rate2 - trade2.open_rate) / trade2.open_rate) * 100
        
        logger.info(f"  入场价: {trade2.open_rate:.6f}")
        logger.info(f"  出场价: {exit_rate2:.6f}")
        logger.info(f"  价格变化: {price_change2:+.2f}% (下跌)")
        logger.info(f"  做空盈亏: {(profit_ratio2 * 100):+.2f}%")
        logger.info(f"  绝对盈亏: {profit_abs2:+.2f} USDT")
        
        # 验证逻辑：价格下跌，做空应该盈利
        if profit_ratio2 > 0:
            logger.info("  ✅ 逻辑正确：价格下跌，做空盈利")
            scenario2_correct = True
        else:
            logger.error("  ❌ 逻辑错误：价格下跌，做空应该盈利")
            scenario2_correct = False
        
        return scenario1_correct and scenario2_correct
        
    except Exception as e:
        logger.error(f"❌ 做空盈亏逻辑测试失败: {e}")
        return False

def test_custom_exit_timezone():
    """测试custom_exit方法的时区处理"""
    logger.info("🔧 测试custom_exit时区处理")
    
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        
        strategy = SMCStrategy()
        
        # 创建时区不匹配的场景
        current_time = datetime.now()  # 无时区
        open_date = datetime.now(timezone.utc) - timedelta(hours=2)  # UTC时区，2小时前
        
        trade = MockTrade(is_short=True, open_date=open_date)
        
        # 测试custom_exit方法
        result = strategy.custom_exit(
            pair="BTC/USDT:USDT",
            trade=trade,
            current_time=current_time,
            current_rate=50000.0,
            current_profit=0.01  # 1% 盈利
        )
        
        logger.info(f"✅ custom_exit执行成功，返回: {result}")
        return True
        
    except Exception as e:
        logger.error(f"❌ custom_exit时区测试失败: {e}")
        return False

def test_real_scenario():
    """测试真实场景数据"""
    logger.info("📈 测试真实场景数据")
    
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        
        strategy = SMCStrategy()
        
        # 基于用户提供的真实数据
        real_scenarios = [
            {
                "pair": "ADA/USDT:USDT",
                "entry_rate": 0.6847,
                "exit_rate": 0.6844,
                "expected_profit": True  # 价格下跌，做空应该盈利
            },
            {
                "pair": "UNI/USDT:USDT", 
                "entry_rate": 7.953,
                "exit_rate": 7.957,
                "expected_profit": False  # 价格上涨，做空应该亏损
            },
            {
                "pair": "BTC/USDT:USDT",
                "entry_rate": 107533.5,
                "exit_rate": 107548.9,
                "expected_profit": False  # 价格上涨，做空应该亏损
            }
        ]
        
        all_correct = True
        
        for scenario in real_scenarios:
            logger.info(f"\n测试 {scenario['pair']}:")
            
            trade = MockTrade(
                is_short=True,
                open_rate=scenario['entry_rate'],
                open_date=datetime.now(timezone.utc) - timedelta(minutes=30)
            )
            
            profit_ratio = trade.calc_profit_ratio(scenario['exit_rate'])
            price_change = ((scenario['exit_rate'] - scenario['entry_rate']) / scenario['entry_rate']) * 100
            
            logger.info(f"  入场价: {scenario['entry_rate']}")
            logger.info(f"  出场价: {scenario['exit_rate']}")
            logger.info(f"  价格变化: {price_change:+.4f}%")
            logger.info(f"  做空盈亏: {(profit_ratio * 100):+.4f}%")
            
            actual_profit = profit_ratio > 0
            if actual_profit == scenario['expected_profit']:
                logger.info("  ✅ 盈亏逻辑正确")
            else:
                logger.error("  ❌ 盈亏逻辑错误")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        logger.error(f"❌ 真实场景测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始SMC策略时区和盈亏修复验证")
    
    test_results = []
    
    # 测试1: 时区修复
    test_results.append(test_timezone_fix())
    
    # 测试2: 做空盈亏逻辑
    test_results.append(test_short_pnl_logic())
    
    # 测试3: custom_exit时区处理
    test_results.append(test_custom_exit_timezone())
    
    # 测试4: 真实场景
    test_results.append(test_real_scenario())
    
    # 总结
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    logger.info(f"\n📊 总测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！修复验证成功")
        logger.info("✅ 时区错误已修复，FreqTrade不再崩溃")
        logger.info("✅ 做空盈亏计算逻辑正确")
        logger.info("✅ 所有datetime操作都是时区安全的")
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} 项测试失败")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
