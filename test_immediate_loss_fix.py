#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC FreqTrade 立即亏损修复效果测试脚本

测试修复后的配置和策略是否能有效减少立即亏损问题
"""

import sys
import logging
import json
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MockDataProvider:
    """模拟数据提供者"""
    def ticker(self, pair):
        # 模拟不同交易对的ticker数据
        tickers = {
            'BTC/USDT:USDT': {'bid': 50000.0, 'ask': 50002.0},
            'ETH/USDT:USDT': {'bid': 3000.0, 'ask': 3001.0},
            'ADA/USDT:USDT': {'bid': 0.6847, 'ask': 0.6849},
        }
        return tickers.get(pair, {'bid': 100.0, 'ask': 100.02})

def test_order_type_optimization():
    """测试订单类型优化"""
    logger.info("🔧 测试订单类型优化")
    
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        
        strategy = SMCStrategy()
        order_types = strategy.order_types
        
        logger.info("📋 优化后的订单类型配置:")
        for key, value in order_types.items():
            logger.info(f"  {key}: {value}")
        
        # 验证关键优化
        success = True
        
        if order_types.get('entry') == 'limit':
            logger.info("✅ 入场订单已优化为限价单")
        else:
            logger.error("❌ 入场订单仍为市价单")
            success = False
        
        if order_types.get('exit') == 'limit':
            logger.info("✅ 出场订单已优化为限价单")
        else:
            logger.error("❌ 出场订单仍为市价单")
            success = False
        
        if order_types.get('stoploss_on_exchange'):
            logger.info("✅ 交易所止损已启用")
        else:
            logger.warning("⚠️ 交易所止损未启用")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 订单类型测试失败: {e}")
        return False

def test_custom_entry_price():
    """测试智能限价单价格计算"""
    logger.info("💰 测试智能限价单价格计算")
    
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        
        strategy = SMCStrategy()
        strategy.dp = MockDataProvider()
        
        test_cases = [
            {
                'pair': 'BTC/USDT:USDT',
                'side': 'long',
                'proposed_rate': 50010.0,
                'expected_improvement': True
            },
            {
                'pair': 'BTC/USDT:USDT', 
                'side': 'short',
                'proposed_rate': 49990.0,
                'expected_improvement': True
            },
            {
                'pair': 'ADA/USDT:USDT',
                'side': 'long',
                'proposed_rate': 0.6850,
                'expected_improvement': True
            }
        ]
        
        success_count = 0
        
        for case in test_cases:
            logger.info(f"\n测试案例: {case['pair']} {case['side']}")
            
            # 获取ticker数据
            ticker = strategy.dp.ticker(case['pair'])
            bid = ticker['bid']
            ask = ticker['ask']
            
            logger.info(f"  市场数据: Bid={bid}, Ask={ask}")
            logger.info(f"  原始价格: {case['proposed_rate']}")
            
            # 计算优化价格
            optimized_price = strategy.custom_entry_price(
                pair=case['pair'],
                current_time=datetime.now(),
                proposed_rate=case['proposed_rate'],
                entry_tag='test',
                side=case['side']
            )
            
            logger.info(f"  优化价格: {optimized_price}")
            
            # 验证优化效果
            if case['side'] == 'long':
                # 做多：优化价格应该在bid和mid之间
                mid_price = (bid + ask) / 2
                if bid <= optimized_price <= mid_price:
                    logger.info("  ✅ 做多价格优化合理")
                    success_count += 1
                else:
                    logger.error("  ❌ 做多价格优化不合理")
            
            elif case['side'] == 'short':
                # 做空：优化价格应该在mid和ask之间
                mid_price = (bid + ask) / 2
                if mid_price <= optimized_price <= ask:
                    logger.info("  ✅ 做空价格优化合理")
                    success_count += 1
                else:
                    logger.error("  ❌ 做空价格优化不合理")
        
        logger.info(f"\n📊 价格优化测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        logger.error(f"❌ 价格优化测试失败: {e}")
        return False

def test_config_optimization():
    """测试配置优化"""
    logger.info("⚙️ 测试配置优化")
    
    try:
        # 检查优化配置文件
        with open("freqtrade-bot/config_optimized.json", 'r') as f:
            config = json.load(f)
        
        logger.info("📋 关键配置检查:")
        
        success = True
        
        # 1. 检查入场价格配置
        entry_pricing = config.get('entry_pricing', {})
        price_side = entry_pricing.get('price_side', 'other')
        
        if price_side == 'same':
            logger.info("✅ 入场价格侧已优化为'same'")
        else:
            logger.error("❌ 入场价格侧仍为'other'")
            success = False
        
        # 2. 检查处理延迟
        process_throttle = config.get('internals', {}).get('process_throttle_secs', 5)
        
        if process_throttle <= 2:
            logger.info(f"✅ 处理延迟已优化为{process_throttle}秒")
        else:
            logger.warning(f"⚠️ 处理延迟仍为{process_throttle}秒")
        
        # 3. 检查最大开仓数
        max_open_trades = config.get('max_open_trades', 15)
        
        if max_open_trades <= 10:
            logger.info(f"✅ 最大开仓数已优化为{max_open_trades}")
        else:
            logger.warning(f"⚠️ 最大开仓数仍为{max_open_trades}")
        
        # 4. 检查市场深度
        depth_check = entry_pricing.get('check_depth_of_market', {})
        delta = depth_check.get('bids_to_ask_delta', 0.1)
        
        if delta >= 0.03:
            logger.info(f"✅ 市场深度阈值已优化为{delta}")
        else:
            logger.warning(f"⚠️ 市场深度阈值仍为{delta}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ 配置优化测试失败: {e}")
        return False

def calculate_expected_improvement():
    """计算预期改善效果"""
    logger.info("📈 计算预期改善效果")
    
    # 修复前的成本分析
    logger.info("修复前成本分析:")
    market_order_fee = 0.0004  # 0.04% Taker费用
    entry_fee_before = market_order_fee
    exit_fee_before = market_order_fee
    slippage_before = 0.0002  # 0.02% 平均滑点
    total_cost_before = entry_fee_before + exit_fee_before + slippage_before
    
    logger.info(f"  入场费用: {entry_fee_before*100:.3f}%")
    logger.info(f"  出场费用: {exit_fee_before*100:.3f}%")
    logger.info(f"  滑点损失: {slippage_before*100:.3f}%")
    logger.info(f"  总成本: {total_cost_before*100:.3f}%")
    
    # 修复后的成本分析
    logger.info("\n修复后成本分析:")
    limit_order_fee = 0.0002  # 0.02% Maker费用
    entry_fee_after = limit_order_fee
    exit_fee_after = limit_order_fee
    slippage_after = 0.0001  # 0.01% 限价单滑点更小
    total_cost_after = entry_fee_after + exit_fee_after + slippage_after
    
    logger.info(f"  入场费用: {entry_fee_after*100:.3f}%")
    logger.info(f"  出场费用: {exit_fee_after*100:.3f}%")
    logger.info(f"  滑点损失: {slippage_after*100:.3f}%")
    logger.info(f"  总成本: {total_cost_after*100:.3f}%")
    
    # 改善效果
    improvement = total_cost_before - total_cost_after
    improvement_pct = improvement / total_cost_before * 100
    
    logger.info(f"\n📊 改善效果:")
    logger.info(f"  成本降低: {improvement*100:.3f}%")
    logger.info(f"  改善比例: {improvement_pct:.1f}%")
    
    if improvement_pct >= 70:
        logger.info("🎉 预期改善效果优秀 (>70%)")
        return True
    elif improvement_pct >= 50:
        logger.info("✅ 预期改善效果良好 (>50%)")
        return True
    else:
        logger.warning("⚠️ 预期改善效果一般 (<50%)")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始SMC FreqTrade立即亏损修复效果测试")
    
    test_results = []
    
    # 测试1: 订单类型优化
    test_results.append(test_order_type_optimization())
    
    # 测试2: 智能限价单价格
    test_results.append(test_custom_entry_price())
    
    # 测试3: 配置优化
    test_results.append(test_config_optimization())
    
    # 测试4: 预期改善效果
    test_results.append(calculate_expected_improvement())
    
    # 总结
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    
    logger.info("="*80)
    logger.info(f"📊 修复效果测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！修复方案验证成功")
        logger.info("✅ 立即亏损问题预期将显著改善")
        logger.info("✅ 建议立即部署优化配置")
        
        logger.info("\n🚀 部署建议:")
        logger.info("1. 备份当前配置: cp freqtrade-bot/config.json freqtrade-bot/config_backup.json")
        logger.info("2. 应用优化配置: cp freqtrade-bot/config_optimized.json freqtrade-bot/config.json")
        logger.info("3. 重启FreqTrade服务")
        logger.info("4. 监控立即亏损改善效果")
        
        return True
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} 项测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
