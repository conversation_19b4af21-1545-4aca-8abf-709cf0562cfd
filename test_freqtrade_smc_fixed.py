#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FreqTrade SMC策略修复后的实际测试脚本
测试在真实FreqTrade环境中的运行情况

功能：
1. 模拟FreqTrade环境加载策略
2. 验证导入警告是否消除
3. 测试策略在多个交易对上的表现
4. 确认日志输出的改进
"""

import sys
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import warnings

# 设置日志格式，模拟FreqTrade的日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def simulate_freqtrade_environment():
    """模拟FreqTrade环境"""
    logger.info("🔧 模拟FreqTrade环境设置")
    
    # 模拟FreqTrade的工作目录变化
    original_cwd = Path.cwd()
    
    try:
        # 模拟从不同目录运行
        test_dir = Path("freqtrade-bot")
        if test_dir.exists():
            import os
            os.chdir(test_dir)
            logger.info(f"切换到FreqTrade目录: {Path.cwd()}")
        
        # 导入策略
        from backtest.strategies.smc_strategy import SMCStrategy
        logger.info("✅ 在FreqTrade环境中成功导入SMC策略")
        
        return SMCStrategy
        
    except Exception as e:
        logger.error(f"❌ FreqTrade环境模拟失败: {e}")
        return None
    finally:
        # 恢复原始工作目录
        import os
        os.chdir(original_cwd)

def create_multiple_pair_data():
    """创建多个交易对的测试数据"""
    pairs = ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'SOL/USDT']
    pair_data = {}
    
    np.random.seed(42)
    
    for i, pair in enumerate(pairs):
        # 为每个交易对生成不同的价格基础
        base_prices = [50000, 3000, 0.5, 25, 100]
        base_price = base_prices[i]
        
        # 生成数据
        length = 200
        dates = pd.date_range('2024-01-01', periods=length, freq='1min')
        
        returns = np.random.normal(0, 0.001, length)
        price = base_price * np.exp(np.cumsum(returns))
        
        data = pd.DataFrame({
            'timestamp': dates,
            'open': price * (1 + np.random.normal(0, 0.0001, length)),
            'high': price * (1 + np.abs(np.random.normal(0, 0.002, length))),
            'low': price * (1 - np.abs(np.random.normal(0, 0.002, length))),
            'close': price,
            'volume': np.random.uniform(100, 1000, length)
        })
        
        # 确保OHLC数据的一致性
        data['high'] = np.maximum(data['high'], data['low'])
        data['high'] = np.maximum(data['high'], data['open'])
        data['high'] = np.maximum(data['high'], data['close'])
        data['low'] = np.minimum(data['low'], data['open'])
        data['low'] = np.minimum(data['low'], data['close'])
        
        data.set_index('timestamp', inplace=True)
        pair_data[pair] = data
    
    return pair_data

def test_strategy_on_multiple_pairs(strategy_class):
    """测试策略在多个交易对上的表现"""
    logger.info("🧪 测试策略在多个交易对上的表现")
    
    pair_data = create_multiple_pair_data()
    results = {}
    
    # 捕获警告
    with warnings.catch_warnings(record=True) as w:
        warnings.simplefilter("always")
        
        for pair, data in pair_data.items():
            try:
                logger.info(f"处理交易对: {pair}")
                
                # 创建策略实例
                strategy = strategy_class()
                
                # 模拟FreqTrade的metadata
                metadata = {'pair': pair}
                
                # 运行完整的策略流程
                indicators_data = strategy.populate_indicators(data.copy(), metadata)
                entry_data = strategy.populate_entry_trend(indicators_data, metadata)
                final_data = strategy.populate_exit_trend(entry_data, metadata)
                
                # 统计信号
                entry_long = final_data.get('enter_long', pd.Series(0, index=final_data.index)).sum()
                entry_short = final_data.get('enter_short', pd.Series(0, index=final_data.index)).sum()
                exit_long = final_data.get('exit_long', pd.Series(0, index=final_data.index)).sum()
                exit_short = final_data.get('exit_short', pd.Series(0, index=final_data.index)).sum()
                
                results[pair] = {
                    'entry_long': entry_long,
                    'entry_short': entry_short,
                    'exit_long': exit_long,
                    'exit_short': exit_short,
                    'total_signals': entry_long + entry_short + exit_long + exit_short
                }
                
                logger.info(f"{pair} - 信号统计: 入场多头={entry_long}, 入场空头={entry_short}, "
                          f"出场多头={exit_long}, 出场空头={exit_short}")
                
            except Exception as e:
                logger.error(f"❌ {pair} 处理失败: {e}")
                results[pair] = {'error': str(e)}
    
    # 检查是否有导入相关的警告
    import_warnings = [warning for warning in w if 'import' in str(warning.message).lower() or 
                      'features' in str(warning.message).lower()]
    
    if import_warnings:
        logger.warning(f"⚠️ 发现 {len(import_warnings)} 个导入相关警告:")
        for warning in import_warnings:
            logger.warning(f"  - {warning.message}")
    else:
        logger.info("✅ 没有发现导入相关警告")
    
    return results

def analyze_results(results):
    """分析测试结果"""
    logger.info("📊 分析测试结果")
    
    successful_pairs = [pair for pair, result in results.items() if 'error' not in result]
    failed_pairs = [pair for pair, result in results.items() if 'error' in result]
    
    logger.info(f"成功处理的交易对: {len(successful_pairs)}/{len(results)}")
    
    if failed_pairs:
        logger.warning(f"失败的交易对: {failed_pairs}")
    
    # 统计总信号数
    total_signals = sum(result.get('total_signals', 0) for result in results.values() 
                       if 'error' not in result)
    
    logger.info(f"总信号数: {total_signals}")
    
    # 详细统计
    for pair, result in results.items():
        if 'error' not in result:
            logger.info(f"{pair}: {result}")
    
    return len(successful_pairs) == len(results)

def main():
    """主测试函数"""
    logger.info("🚀 开始FreqTrade SMC策略修复后的实际测试")
    
    # 测试1: 模拟FreqTrade环境
    strategy_class = simulate_freqtrade_environment()
    if not strategy_class:
        logger.error("❌ FreqTrade环境模拟失败")
        return False
    
    # 测试2: 多交易对测试
    results = test_strategy_on_multiple_pairs(strategy_class)
    
    # 测试3: 结果分析
    success = analyze_results(results)
    
    if success:
        logger.info("🎉 FreqTrade SMC策略修复验证成功！")
        logger.info("✅ 导入依赖问题已解决")
        logger.info("✅ 警告日志已优化")
        logger.info("✅ 策略功能完整性已确认")
    else:
        logger.error("❌ 测试中发现问题，需要进一步修复")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
