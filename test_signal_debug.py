#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的信号调试脚本 - 使用FreqTrade标准方法
找出为什么信号生成为0的真正原因
"""

import pandas as pd
import numpy as np
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.storage.optimized_storage import OptimizedStorage
from backtest.strategies.smc_strategy import SMCStrategy
from config.smc_strategy_config import SMCConfigManager

def debug_signals():
    print("🔍 信号调试 - 使用FreqTrade标准方法")
    print("=" * 50)
    
    # 加载数据
    storage = OptimizedStorage('./data/storage/data')
    data = storage.load_data('BTC_USDT', '1m')
    print(f"数据长度: {len(data)}")
    
    # 创建策略 - FreqTrade方式
    config_manager = SMCConfigManager()
    strategy_params = config_manager.get_strategy_params()
    print(f"策略参数: {strategy_params}")
    
    # FreqTrade需要config参数
    config = {'timeframe': '1m'}
    strategy_params['config'] = config
    strategy = SMCStrategy(**strategy_params)
    
    # 使用FreqTrade标准方法
    print("\n使用FreqTrade标准信号管理...")
    
    # 1. 计算指标
    dataframe = strategy.populate_indicators(data.copy(), {})
    print(f"指标计算完成，dataframe列: {list(dataframe.columns)}")
    
    # 检查关键指标
    if 'EMA_20' in dataframe.columns and 'EMA_50' in dataframe.columns:
        print(f"EMA_20存在: ✅")
        print(f"EMA_50存在: ✅")
    else:
        print("❌ EMA指标缺失！")
        return
    
    if 'RSI' in dataframe.columns:
        print(f"RSI存在: ✅")
    else:
        print("❌ RSI指标缺失！")
        return
    
    if 'ATR' in dataframe.columns:
        print(f"ATR存在: ✅")
    else:
        print("❌ ATR指标缺失！")
        return
    
    # 2. 生成入场信号
    dataframe = strategy.populate_entry_trend(dataframe, {})
    
    # 3. 生成出场信号
    dataframe = strategy.populate_exit_trend(dataframe, {})
    
    # 检查信号生成
    if 'enter_long' in dataframe.columns:
        long_signals = dataframe['enter_long'].sum()
        print(f"多头信号数量: {long_signals}")
    else:
        print("❌ 多头信号列缺失！")
        long_signals = 0
    
    if 'enter_short' in dataframe.columns:
        short_signals = dataframe['enter_short'].sum()
        print(f"空头信号数量: {short_signals}")
    else:
        print("❌ 空头信号列缺失！")
        short_signals = 0
    
    total_signals = long_signals + short_signals
    print(f"总信号数量: {total_signals}")
    
    if total_signals > 0:
        print("✅ FreqTrade信号生成成功！")
        
        # 显示前几个信号
        if long_signals > 0:
            long_indices = dataframe[dataframe['enter_long'] == 1].index[:5]
            print(f"前5个多头信号位置: {long_indices.tolist()}")
        
        if short_signals > 0:
            short_indices = dataframe[dataframe['enter_short'] == 1].index[:5]
            print(f"前5个空头信号位置: {short_indices.tolist()}")
    else:
        print("❌ 仍然没有信号生成")
        
        # 检查具体条件
        print("\n检查信号条件...")
        i = 1000  # 检查第1000个数据点
        
        if i < len(dataframe):
            print(f"第{i}个数据点:")
            print(f"EMA_20: {dataframe['EMA_20'].iloc[i]:.6f}")
            print(f"EMA_50: {dataframe['EMA_50'].iloc[i]:.6f}")
            print(f"RSI: {dataframe['RSI'].iloc[i]:.2f}")
            print(f"ATR: {dataframe['ATR'].iloc[i]:.6f}")
            print(f"价格: {dataframe['close'].iloc[i]:.6f}")
            
            # 检查多头条件
            ema_trend = dataframe['EMA_20'].iloc[i] > dataframe['EMA_50'].iloc[i]
            rsi_range = 30 < dataframe['RSI'].iloc[i] < 70
            price_above_ema = dataframe['close'].iloc[i] > dataframe['EMA_20'].iloc[i]
            rsi_not_overbought = dataframe['RSI'].iloc[i] < 75
            atr_ok = dataframe['ATR'].iloc[i] < dataframe['ATR'].rolling(20).mean().iloc[i] * 2
            volume_ok = dataframe['volume'].iloc[i] > 0
            
            print(f"多头条件检查:")
            print(f"  EMA趋势: {ema_trend}")
            print(f"  RSI范围: {rsi_range}")
            print(f"  价格位置: {price_above_ema}")
            print(f"  RSI不超买: {rsi_not_overbought}")
            print(f"  ATR正常: {atr_ok}")
            print(f"  成交量OK: {volume_ok}")
            
            all_conditions = ema_trend & rsi_range & price_above_ema & rsi_not_overbought & atr_ok & volume_ok
            print(f"所有条件满足: {all_conditions}")

if __name__ == "__main__":
    debug_signals() 