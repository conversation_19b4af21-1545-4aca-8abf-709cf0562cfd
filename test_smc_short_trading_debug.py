#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略做空交易调试脚本
专门用于分析和验证做空交易的盈亏逻辑

功能：
1. 模拟做空交易场景
2. 验证盈亏计算逻辑
3. 分析信号生成条件
4. 对比预期与实际结果
"""

import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def create_downtrend_data(length=500, start_price=50000):
    """
    创建明显下跌趋势的测试数据
    这种数据下，做空应该盈利
    """
    np.random.seed(42)

    dates = pd.date_range('2024-01-01', periods=length, freq='1min')

    # 🔧 修复：创建更合理的下跌趋势
    # 总体下跌5%，分布在整个时间段
    trend_factor = np.linspace(1.0, 0.95, length)  # 从100%到95%

    # 添加随机波动，但保持总体趋势
    noise = np.random.normal(0, 0.001, length)  # 0.1%的随机波动
    daily_returns = np.diff(np.log(trend_factor)) + noise[1:]

    # 生成价格序列
    prices = [start_price]
    for i in range(1, length):
        new_price = prices[-1] * np.exp(daily_returns[i-1])
        prices.append(new_price)

    prices = np.array(prices)

    # 生成OHLCV数据
    data = pd.DataFrame({
        'timestamp': dates,
        'open': prices * (1 + np.random.normal(0, 0.0002, length)),
        'high': prices * (1 + np.abs(np.random.normal(0, 0.0005, length))),
        'low': prices * (1 - np.abs(np.random.normal(0, 0.0005, length))),
        'close': prices,
        'volume': np.random.uniform(100, 1000, length)
    })

    # 确保OHLC数据的一致性
    data['high'] = np.maximum(data['high'], data['low'])
    data['high'] = np.maximum(data['high'], data['open'])
    data['high'] = np.maximum(data['high'], data['close'])
    data['low'] = np.minimum(data['low'], data['open'])
    data['low'] = np.minimum(data['low'], data['close'])

    data.set_index('timestamp', inplace=True)
    return data

def test_short_signal_generation():
    """测试做空信号生成"""
    logger.info("🧪 测试做空信号生成")
    
    try:
        from backtest.strategies.smc_strategy import SMCStrategy
        
        # 创建下跌趋势数据
        test_data = create_downtrend_data(300, 50000)
        logger.info(f"创建下跌趋势数据: {len(test_data)} 行")
        logger.info(f"价格变化: {test_data['close'].iloc[0]:.2f} -> {test_data['close'].iloc[-1]:.2f}")
        logger.info(f"总跌幅: {((test_data['close'].iloc[-1] / test_data['close'].iloc[0]) - 1) * 100:.2f}%")
        
        # 创建策略实例
        strategy = SMCStrategy()
        metadata = {'pair': 'BTC/USDT'}
        
        # 计算指标
        indicators_data = strategy.populate_indicators(test_data.copy(), metadata)
        
        # 生成入场信号
        entry_data = strategy.populate_entry_trend(indicators_data, metadata)
        
        # 生成出场信号
        final_data = strategy.populate_exit_trend(entry_data, metadata)
        
        # 分析信号
        analyze_short_signals(final_data, metadata['pair'])
        
        return final_data
        
    except Exception as e:
        logger.error(f"❌ 做空信号测试失败: {e}")
        return None

def analyze_short_signals(data: pd.DataFrame, pair: str):
    """分析做空信号"""
    logger.info(f"📊 分析做空信号 - {pair}")
    
    # 统计信号数量
    short_entries = data.get('enter_short', pd.Series(0, index=data.index)).sum()
    short_exits = data.get('exit_short', pd.Series(0, index=data.index)).sum()
    
    logger.info(f"做空入场信号: {short_entries}")
    logger.info(f"做空出场信号: {short_exits}")
    
    if short_entries == 0:
        logger.warning("⚠️ 没有生成做空入场信号")
        return
    
    # 分析入场信号的条件
    entry_indices = data[data.get('enter_short', 0) == 1].index
    logger.info(f"做空入场时间点: {len(entry_indices)} 个")
    
    for i, idx in enumerate(entry_indices[:5]):  # 只分析前5个信号
        row = data.loc[idx]
        logger.info(f"入场信号 {i+1} @ {idx}:")
        logger.info(f"  价格: {row['close']:.2f}")
        logger.info(f"  EMA20: {row['EMA_20']:.2f}, EMA50: {row['EMA_50']:.2f}")
        logger.info(f"  RSI: {row['RSI']:.2f}")
        logger.info(f"  趋势确认: {row['EMA_20'] < row['EMA_50']}")
        logger.info(f"  价格位置: {row['close'] < row['EMA_20']}")
    
    # 分析出场信号
    if short_exits > 0:
        exit_indices = data[data.get('exit_short', 0) == 1].index
        logger.info(f"做空出场时间点: {len(exit_indices)} 个")
        
        for i, idx in enumerate(exit_indices[:3]):  # 只分析前3个信号
            row = data.loc[idx]
            logger.info(f"出场信号 {i+1} @ {idx}:")
            logger.info(f"  价格: {row['close']:.2f}")
            logger.info(f"  EMA20: {row['EMA_20']:.2f}, EMA50: {row['EMA_50']:.2f}")
            logger.info(f"  RSI: {row['RSI']:.2f}")

def simulate_short_trades(data: pd.DataFrame):
    """模拟做空交易执行"""
    logger.info("💰 模拟做空交易执行")
    
    entry_signals = data[data.get('enter_short', 0) == 1]
    exit_signals = data[data.get('exit_short', 0) == 1]
    
    if len(entry_signals) == 0:
        logger.warning("⚠️ 没有做空入场信号，无法模拟交易")
        return
    
    trades = []
    open_trade = None
    
    for idx in data.index:
        # 检查入场信号
        if data.loc[idx].get('enter_short', 0) == 1 and open_trade is None:
            open_trade = {
                'entry_time': idx,
                'entry_price': data.loc[idx]['close'],
                'pair': 'BTC/USDT'
            }
            logger.info(f"🔻 开仓做空 @ {idx}: 价格 {open_trade['entry_price']:.2f}")
        
        # 检查出场信号
        elif data.loc[idx].get('exit_short', 0) == 1 and open_trade is not None:
            exit_price = data.loc[idx]['close']
            entry_price = open_trade['entry_price']
            
            # 计算做空盈亏：入场价 > 出场价 = 盈利
            profit_ratio = (entry_price - exit_price) / entry_price
            profit_abs = (entry_price - exit_price) * 1000 / entry_price  # 假设1000 USDT仓位
            
            trade = {
                **open_trade,
                'exit_time': idx,
                'exit_price': exit_price,
                'profit_ratio': profit_ratio,
                'profit_abs': profit_abs,
                'duration': idx - open_trade['entry_time']
            }
            
            trades.append(trade)
            
            logger.info(f"🔺 平仓做空 @ {idx}: 价格 {exit_price:.2f}")
            logger.info(f"  入场价: {entry_price:.2f}, 出场价: {exit_price:.2f}")
            logger.info(f"  价格变化: {((exit_price - entry_price) / entry_price * 100):+.2f}%")
            logger.info(f"  做空盈亏: {(profit_ratio * 100):+.2f}% ({profit_abs:+.2f} USDT)")
            logger.info(f"  持仓时间: {trade['duration']}")
            
            open_trade = None
    
    # 统计交易结果
    if trades:
        total_profit = sum(t['profit_abs'] for t in trades)
        win_trades = [t for t in trades if t['profit_ratio'] > 0]
        win_rate = len(win_trades) / len(trades) * 100
        
        logger.info(f"📈 交易统计:")
        logger.info(f"  总交易数: {len(trades)}")
        logger.info(f"  盈利交易: {len(win_trades)}")
        logger.info(f"  胜率: {win_rate:.1f}%")
        logger.info(f"  总盈亏: {total_profit:+.2f} USDT")
        
        if total_profit < 0:
            logger.warning("⚠️ 做空交易总体亏损！需要检查策略逻辑")
        else:
            logger.info("✅ 做空交易总体盈利")
    
    return trades

def plot_trading_analysis(data: pd.DataFrame, trades: list):
    """绘制交易分析图表"""
    try:
        plt.figure(figsize=(15, 10))
        
        # 子图1: 价格和EMA
        plt.subplot(3, 1, 1)
        plt.plot(data.index, data['close'], label='Close Price', linewidth=1)
        plt.plot(data.index, data['EMA_20'], label='EMA 20', alpha=0.7)
        plt.plot(data.index, data['EMA_50'], label='EMA 50', alpha=0.7)
        
        # 标记交易点
        for trade in trades:
            plt.axvline(trade['entry_time'], color='red', alpha=0.5, linestyle='--', label='Short Entry' if trade == trades[0] else "")
            plt.axvline(trade['exit_time'], color='green', alpha=0.5, linestyle='--', label='Short Exit' if trade == trades[0] else "")
        
        plt.title('Price and EMAs with Short Trading Signals')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图2: RSI
        plt.subplot(3, 1, 2)
        plt.plot(data.index, data['RSI'], label='RSI', color='purple')
        plt.axhline(30, color='red', linestyle='--', alpha=0.5, label='Oversold')
        plt.axhline(70, color='red', linestyle='--', alpha=0.5, label='Overbought')
        plt.title('RSI Indicator')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 子图3: 交易盈亏
        plt.subplot(3, 1, 3)
        if trades:
            trade_times = [t['exit_time'] for t in trades]
            trade_profits = [t['profit_abs'] for t in trades]
            colors = ['green' if p > 0 else 'red' for p in trade_profits]
            plt.bar(trade_times, trade_profits, color=colors, alpha=0.7)
            plt.title('Short Trading P&L')
            plt.ylabel('Profit (USDT)')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('smc_short_trading_analysis.png', dpi=300, bbox_inches='tight')
        logger.info("📊 交易分析图表已保存: smc_short_trading_analysis.png")
        
    except Exception as e:
        logger.warning(f"绘图失败: {e}")

def main():
    """主函数"""
    logger.info("🚀 开始SMC做空交易调试")
    
    # 测试1: 信号生成
    data = test_short_signal_generation()
    if data is None:
        return False
    
    # 测试2: 交易模拟
    trades = simulate_short_trades(data)
    
    # 测试3: 结果分析
    if trades:
        plot_trading_analysis(data, trades)
    
    logger.info("🎉 SMC做空交易调试完成")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
