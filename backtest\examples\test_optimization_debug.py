#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SMC策略优化问题诊断脚本

专门用于诊断和修复优化器中的问题
"""

import pandas as pd
import numpy as np
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from data.storage.optimized_storage import OptimizedStorage
from backtest.strategies.optimization.smc_optimizer import SMCOptimizer, FreqTradeOptimizationEngine
from config.smc_strategy_config import SMCConfigManager
from backtest.strategies.smc_strategy import SMCStrategy

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_single_param_evaluation():
    """测试单个参数组合的评估"""
    print("=== 测试单个参数组合评估 ===")
    
    # 加载数据
    storage_dir = "./data/storage/data"
    storage = OptimizedStorage(storage_dir)
    
    symbol = 'BTC_USDT'
    timeframe = '1m'
    
    if not storage.has_data(symbol, timeframe):
        print(f"错误: {symbol} 的数据不存在")
        return False
    
    data = storage.load_data(symbol, timeframe)
    # 使用较小的数据集进行测试
    data = data.tail(1000)
    print(f"测试数据长度: {len(data)}")
    
    # 创建配置管理器
    config_manager = SMCConfigManager()
    
    # 获取默认参数
    base_params = config_manager.get_strategy_params()
    print(f"基础参数: {base_params}")
    
    # 创建策略实例
    try:
        config = {'timeframe': '1m'}
        base_params['config'] = config
        strategy = SMCStrategy(**base_params)
        print("策略创建成功")
    except Exception as e:
        print(f"策略创建失败: {e}")
        return False
    
    # 创建回测引擎
    try:
        engine = FreqTradeOptimizationEngine(data.copy(), initial_cash=10000)
        print("回测引擎创建成功")
    except Exception as e:
        print(f"回测引擎创建失败: {e}")
        return False
    
    # 运行回测
    try:
        results = engine.run(strategy)
        print("回测运行成功")
        
        # 检查结果
        print(f"回测结果类型: {type(results)}")
        print(f"指标: {results.metrics}")
        print(f"交易记录: {type(results.trades)}, 长度: {len(results.trades)}")
        
        # 检查关键指标
        metrics = results.metrics
        trades_count = len(results.trades) if not results.trades.empty else 0
        
        print(f"交易数量: {trades_count}")
        print(f"总收益: {metrics.get('total_return', 0)}")
        print(f"夏普比率: {metrics.get('sharpe_ratio', 0)}")
        
        return True
        
    except Exception as e:
        print(f"回测运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_optimization_params():
    """测试优化参数配置"""
    print("\n=== 测试优化参数配置 ===")
    
    config_manager = SMCConfigManager()
    
    # 检查优化参数
    opt_params = config_manager.get_optimization_params()
    print(f"优化参数: {opt_params}")
    
    # 检查优化网格
    opt_grid = config_manager.get_optimization_grid()
    print(f"优化网格参数数量: {len(opt_grid)}")
    
    # 计算总组合数
    total_combinations = 1
    for param_name, param_values in opt_grid.items():
        total_combinations *= len(param_values)
        print(f"  {param_name}: {len(param_values)} 个值")
    
    print(f"总参数组合数: {total_combinations}")
    
    # 检查关键设置
    min_trades = opt_params.get('min_trades_required', 50)
    target_metric = opt_params.get('target_metric', 'sharpe_ratio')
    max_combinations = opt_params.get('max_combinations', 1000)
    
    print(f"最小交易数要求: {min_trades}")
    print(f"目标指标: {target_metric}")
    print(f"最大组合数限制: {max_combinations}")
    
    return opt_params, opt_grid

def test_optimizer_evaluation():
    """测试优化器的评估逻辑"""
    print("\n=== 测试优化器评估逻辑 ===")
    
    # 加载数据
    storage_dir = "./data/storage/data"
    storage = OptimizedStorage(storage_dir)
    
    symbol = 'BTC_USDT'
    timeframe = '1m'
    data = storage.load_data(symbol, timeframe)
    data = data.tail(2000)  # 使用2000行数据
    
    # 创建优化器
    config_manager = SMCConfigManager()
    optimizer = SMCOptimizer(
        data=data,
        config_manager=config_manager,
        engine_class=FreqTradeOptimizationEngine
    )
    
    # 生成少量参数组合进行测试
    param_combinations = optimizer._generate_param_combinations()
    print(f"生成的参数组合数: {len(param_combinations)}")
    
    # 测试前5个组合
    test_combinations = param_combinations[:5]
    
    valid_results = 0
    for i, params in enumerate(test_combinations):
        print(f"\n测试组合 {i+1}:")
        try:
            result = optimizer._evaluate_params(params)
            if result is not None:
                valid_results += 1
                print(f"  有效结果: 得分={result['score']:.4f}, 交易数={result['trades_count']}")
            else:
                print(f"  无效结果: 可能交易数不足")
        except Exception as e:
            print(f"  评估失败: {e}")
    
    print(f"\n有效结果数: {valid_results}/{len(test_combinations)}")
    
    return valid_results > 0

def main():
    """主测试函数"""
    print("SMC策略优化问题诊断")
    print("=" * 50)
    
    # 测试1: 单个参数评估
    success1 = test_single_param_evaluation()
    
    # 测试2: 优化参数配置
    opt_params, opt_grid = test_optimization_params()
    
    # 测试3: 优化器评估逻辑
    success3 = test_optimizer_evaluation()
    
    print("\n" + "=" * 50)
    print("诊断结果总结:")
    print(f"单个参数评估: {'成功' if success1 else '失败'}")
    print(f"优化参数配置: 正常")
    print(f"优化器评估逻辑: {'成功' if success3 else '失败'}")
    
    # 分析问题
    if not success1:
        print("\n问题分析: 单个参数评估失败")
        print("可能原因:")
        print("1. 策略实例化问题")
        print("2. 回测引擎问题")
        print("3. 数据格式问题")
    
    if not success3:
        print("\n问题分析: 优化器评估逻辑失败")
        print("可能原因:")
        print("1. 最小交易数要求过高")
        print("2. 参数组合无效")
        print("3. 评分计算问题")
    
    # 提供修复建议
    print("\n修复建议:")
    min_trades = opt_params.get('min_trades_required', 50)
    if min_trades > 10:
        print(f"1. 降低最小交易数要求 (当前: {min_trades}, 建议: 10)")
    
    print("2. 检查回测引擎的交易生成逻辑")
    print("3. 验证策略信号生成是否正常")
    
    return success1 and success3

if __name__ == "__main__":
    main()
