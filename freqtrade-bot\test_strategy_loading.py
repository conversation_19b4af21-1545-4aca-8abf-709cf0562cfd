#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
FreqTrade策略加载测试脚本

测试SMCStrategy是否可以被FreqTrade正确加载和使用
"""

import sys
import os
from pathlib import Path

# 添加策略路径
strategy_paths = [
    "user_data/strategies/",
    "E:/newADM/gitRepository/AriQuantification/backtest/strategies/"
]

for path in strategy_paths:
    if os.path.exists(path):
        sys.path.insert(0, path)

def test_strategy_import():
    """测试策略导入"""
    print("=== 测试策略导入 ===")
    
    try:
        # 尝试从FreqTrade策略目录导入
        from SMCStrategy import SMCStrategy
        print("成功从FreqTrade策略目录导入SMCStrategy")
        return SMCStrategy
    except ImportError as e:
        print(f"从FreqTrade策略目录导入失败: {e}")

    try:
        # 尝试从项目目录导入
        sys.path.insert(0, "E:/newADM/gitRepository/AriQuantification")
        from backtest.strategies.smc_strategy import SMCStrategy
        print("成功从项目目录导入SMCStrategy")
        return SMCStrategy
    except ImportError as e:
        print(f"从项目目录导入失败: {e}")
        return None

def test_strategy_instantiation(strategy_class):
    """测试策略实例化"""
    print("\n=== 测试策略实例化 ===")
    
    if strategy_class is None:
        print("策略类为空，无法测试实例化")
        return None

    try:
        # 创建基本配置
        config = {
            'timeframe': '1m',
            'stake_currency': 'USDT',
            'dry_run': True
        }

        # 实例化策略
        strategy = strategy_class(config=config)
        print("策略实例化成功")
        
        # 检查必要的FreqTrade方法
        required_methods = [
            'populate_indicators',
            'populate_entry_trend', 
            'populate_exit_trend'
        ]
        
        for method in required_methods:
            if hasattr(strategy, method):
                print(f"方法 {method} 存在")
            else:
                print(f"方法 {method} 不存在")

        # 检查策略配置
        if hasattr(strategy, 'timeframe'):
            print(f"时间框架: {strategy.timeframe}")
        if hasattr(strategy, 'stoploss'):
            print(f"止损: {strategy.stoploss}")
        if hasattr(strategy, 'minimal_roi'):
            print(f"最小ROI: {strategy.minimal_roi}")
            
        return strategy
        
    except Exception as e:
        print(f"策略实例化失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_strategy_methods(strategy):
    """测试策略方法"""
    print("\n=== 测试策略方法 ===")
    
    if strategy is None:
        print("策略实例为空，无法测试方法")
        return False
    
    try:
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        dates = pd.date_range('2024-01-01', periods=100, freq='1min')
        test_data = pd.DataFrame({
            'open': np.random.uniform(40000, 50000, 100),
            'high': np.random.uniform(40000, 50000, 100),
            'low': np.random.uniform(40000, 50000, 100),
            'close': np.random.uniform(40000, 50000, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        }, index=dates)
        
        # 确保OHLC数据逻辑正确
        test_data['high'] = test_data[['open', 'close']].max(axis=1) + np.random.uniform(0, 100, 100)
        test_data['low'] = test_data[['open', 'close']].min(axis=1) - np.random.uniform(0, 100, 100)
        
        metadata = {'pair': 'BTC/USDT'}
        
        # 测试指标计算
        print("测试 populate_indicators...")
        result_indicators = strategy.populate_indicators(test_data.copy(), metadata)
        print(f"populate_indicators 成功，返回 {len(result_indicators)} 行数据")

        # 测试入场信号
        print("测试 populate_entry_trend...")
        result_entry = strategy.populate_entry_trend(result_indicators.copy(), metadata)
        print(f"populate_entry_trend 成功，返回 {len(result_entry)} 行数据")
        
        # 检查信号列
        signal_columns = ['enter_long', 'enter_short', 'enter_tag']
        for col in signal_columns:
            if col in result_entry.columns:
                signals = result_entry[col].sum() if col != 'enter_tag' else result_entry[col].notna().sum()
                print(f"信号列 {col}: {signals} 个信号")

        # 测试出场信号
        print("测试 populate_exit_trend...")
        result_exit = strategy.populate_exit_trend(result_entry.copy(), metadata)
        print(f"populate_exit_trend 成功，返回 {len(result_exit)} 行数据")

        # 检查出场信号列
        exit_columns = ['exit_long', 'exit_short', 'exit_tag']
        for col in exit_columns:
            if col in result_exit.columns:
                signals = result_exit[col].sum() if col != 'exit_tag' else result_exit[col].notna().sum()
                print(f"出场信号列 {col}: {signals} 个信号")
        
        return True
        
    except Exception as e:
        print(f"策略方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("FreqTrade SMC策略加载测试")
    print("=" * 50)
    
    # 测试1: 策略导入
    strategy_class = test_strategy_import()
    
    # 测试2: 策略实例化
    strategy = test_strategy_instantiation(strategy_class)
    
    # 测试3: 策略方法
    methods_ok = test_strategy_methods(strategy)
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    print(f"策略导入: {'成功' if strategy_class else '失败'}")
    print(f"策略实例化: {'成功' if strategy else '失败'}")
    print(f"策略方法: {'成功' if methods_ok else '失败'}")

    if strategy_class and strategy and methods_ok:
        print("\n所有测试通过！策略可以被FreqTrade正确加载和使用")
        return True
    else:
        print("\n存在问题，需要进一步调试")
        return False

if __name__ == "__main__":
    main()
